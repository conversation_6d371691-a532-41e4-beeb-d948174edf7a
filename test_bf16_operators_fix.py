#!/usr/bin/env python3
# Copyright (c) 2025 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Test script to verify BF16 operators fix for cc70 compatibility.
"""

import os
import sys
import subprocess
import paddle

def test_bf16_operators_compilation():
    """Test compilation of BF16 operators."""
    print("🔨 Testing BF16 operators compilation...")
    
    # Create a test file that uses BF16 operators
    test_content = '''
#include "custom_ops/gpu_ops/moe/moe_compatibility.h"
#include <cuda_fp16.h>

__global__ void test_bf16_operators() {
    __nv_bfloat16 a = __float2bfloat16(1.0f);
    __nv_bfloat16 b = __float2bfloat16(2.0f);
    float c = 3.0f;
    
    // Test operators that were causing compilation errors
    a *= c;      // operator*=(__nv_bfloat16&, float)
    a += b;      // operator+=(__nv_bfloat16&, const __nv_bfloat16&)
    a -= b;      // operator-=(__nv_bfloat16&, const __nv_bfloat16&)
    a /= b;      // operator/=(__nv_bfloat16&, const __nv_bfloat16&)
    
    // Test FP16 operators too
    __half h1 = __float2half(1.0f);
    __half h2 = __float2half(2.0f);
    
    h1 *= c;     // operator*=(__half&, float)
    h1 += h2;    // operator+=(__half&, const __half&)
    h1 -= h2;    // operator-=(__half&, const __half&)
    h1 /= h2;    // operator/=(__half&, const __half&)
}

int main() {
    return 0;
}
'''
    
    try:
        with open("test_bf16_operators.cu", "w") as f:
            f.write(test_content)
        
        # Get compute capability
        cc = 70  # Default
        try:
            if paddle.is_compiled_with_cuda():
                device_id = paddle.get_device().split(':')[-1]
                prop = paddle.device.cuda.get_device_properties(int(device_id))
                cc = prop.major * 10 + prop.minor
        except:
            pass
        
        print(f"Testing with compute capability: {cc}")
        
        # Test compilation
        nvcc_cmd = [
            "nvcc", "-c", "test_bf16_operators.cu",
            "-o", "test_bf16_operators.o",
            "-std=c++17",
            f"-gencode=arch=compute_{cc},code=sm_{cc}",
            "-I.",
            "-DMOE_COMPATIBILITY_CC70=1" if cc < 80 else "",
        ]
        
        # Remove empty strings
        nvcc_cmd = [arg for arg in nvcc_cmd if arg]
        
        result = subprocess.run(nvcc_cmd, capture_output=True, text=True, cwd=".")
        
        # Clean up
        for temp_file in ["test_bf16_operators.cu", "test_bf16_operators.o"]:
            if os.path.exists(temp_file):
                os.remove(temp_file)
        
        if result.returncode == 0:
            print("✅ BF16 operators compilation successful")
            return True
        else:
            print("❌ BF16 operators compilation failed:")
            print("STDERR:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_moe_deepgemm_depermute_compilation():
    """Test compilation of the fixed moe_deepgemm_depermute.cu file."""
    print("\n🔨 Testing moe_deepgemm_depermute.cu compilation...")
    
    depermute_file = "custom_ops/gpu_ops/moe/moe_deepgemm_depermute.cu"
    if not os.path.exists(depermute_file):
        print(f"❌ File not found: {depermute_file}")
        return False
    
    try:
        # Get compute capability
        cc = 70  # Default
        try:
            if paddle.is_compiled_with_cuda():
                device_id = paddle.get_device().split(':')[-1]
                prop = paddle.device.cuda.get_device_properties(int(device_id))
                cc = prop.major * 10 + prop.minor
        except:
            pass
        
        print(f"Testing with compute capability: {cc}")
        
        # Test compilation
        nvcc_cmd = [
            "nvcc", "-c", depermute_file,
            "-o", "test_moe_depermute.o",
            "-std=c++17",
            f"-gencode=arch=compute_{cc},code=sm_{cc}",
            "-Icustom_ops/gpu_ops",
            "-Icustom_ops/gpu_ops/moe",
            "-DMOE_COMPATIBILITY_CC70=1" if cc < 80 else "",
        ]
        
        # Remove empty strings
        nvcc_cmd = [arg for arg in nvcc_cmd if arg]
        
        result = subprocess.run(nvcc_cmd, capture_output=True, text=True, cwd=".")
        
        # Clean up
        if os.path.exists("test_moe_depermute.o"):
            os.remove("test_moe_depermute.o")
        
        if result.returncode == 0:
            print("✅ moe_deepgemm_depermute.cu compilation successful")
            return True
        else:
            print("❌ moe_deepgemm_depermute.cu compilation failed:")
            print("STDERR:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def check_compatibility_header():
    """Check if the compatibility header has the BF16 operators."""
    print("\n🔍 Checking compatibility header...")
    
    header_file = "custom_ops/gpu_ops/moe/moe_compatibility.h"
    if not os.path.exists(header_file):
        print(f"❌ Header file not found: {header_file}")
        return False
    
    try:
        with open(header_file, 'r') as f:
            content = f.read()
        
        # Check for BF16 operators
        bf16_operators = [
            "__nv_bfloat16& operator*=(__nv_bfloat16& a, float b)",
            "__nv_bfloat16& operator+=(__nv_bfloat16& a, const __nv_bfloat16& b)",
            "__nv_bfloat16& operator-=(__nv_bfloat16& a, const __nv_bfloat16& b)",
            "__nv_bfloat16& operator/=(__nv_bfloat16& a, const __nv_bfloat16& b)",
        ]
        
        # Check for FP16 operators
        fp16_operators = [
            "__half& operator*=(__half& a, float b)",
            "__half& operator+=(__half& a, const __half& b)",
            "__half& operator-=(__half& a, const __half& b)",
            "__half& operator/=(__half& a, const __half& b)",
        ]
        
        missing_operators = []
        
        for op in bf16_operators:
            if op in content:
                print(f"✅ Found BF16 operator: {op.split('(')[0]}")
            else:
                print(f"❌ Missing BF16 operator: {op.split('(')[0]}")
                missing_operators.append(op)
        
        for op in fp16_operators:
            if op in content:
                print(f"✅ Found FP16 operator: {op.split('(')[0]}")
            else:
                print(f"❌ Missing FP16 operator: {op.split('(')[0]}")
                missing_operators.append(op)
        
        if missing_operators:
            print(f"\n❌ Missing {len(missing_operators)} operators")
            return False
        else:
            print(f"\n✅ All operators found in compatibility header")
            return True
            
    except Exception as e:
        print(f"❌ Failed to check compatibility header: {e}")
        return False

def main():
    """Run all tests."""
    print("BF16 Operators Fix Test")
    print("=" * 30)
    
    if not paddle.is_compiled_with_cuda():
        print("❌ CUDA not available, skipping tests")
        return False
    
    tests = [
        ("Compatibility Header Check", check_compatibility_header),
        ("BF16 Operators Compilation", test_bf16_operators_compilation),
        ("MOE DeepGEMM DePermute Compilation", test_moe_deepgemm_depermute_compilation),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Running {test_name} test...")
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 30)
    print("Test Summary:")
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! BF16 operators fix is working.")
        print("\n💡 Next steps:")
        print("   1. cd custom_ops")
        print("   2. python setup_ops.py build_ext --inplace")
        return True
    else:
        print("\n⚠️  Some tests failed. Please check the fixes.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
