#!/usr/bin/env python3
# Copyright (c) 2025 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Comprehensive test script for all MOE cc70 compatibility fixes.
Tests Marlin async memory copy fixes, BF16 operators, and overall compilation.
"""

import os
import sys
import subprocess
import paddle

def get_compute_capability():
    """Get CUDA compute capability."""
    try:
        if paddle.is_compiled_with_cuda():
            device_id = paddle.get_device().split(':')[-1]
            prop = paddle.device.cuda.get_device_properties(int(device_id))
            return prop.major * 10 + prop.minor
    except:
        pass
    return 70  # Default

def test_required_files():
    """Test if all required files exist."""
    print("📁 Checking required files...")
    
    required_files = [
        "custom_ops/gpu_ops/moe/moe_compatibility.h",
        "custom_ops/gpu_ops/moe/moe_simple_cc70.cu",
        "custom_ops/gpu_ops/moe/moe_deepgemm_depermute.cu",
        "custom_ops/gpu_ops/moe/moe_deepgemm_permute.cu",
        "custom_ops/gpu_ops/moe/gptq_marlin_repack.cu",
        "custom_ops/gpu_ops/moe/moe_wna16_marlin_utils/marlin.cuh",
        "fastdeploy/model_executor/layers/moe/fused_moe_cc70_backend.py",
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n❌ Missing {len(missing_files)} required files")
        return False
    else:
        print(f"\n✅ All {len(required_files)} required files found")
        return True

def test_marlin_async_fix():
    """Test Marlin async memory copy fix."""
    print("\n🔧 Testing Marlin async memory copy fix...")
    
    marlin_header = "custom_ops/gpu_ops/moe/moe_wna16_marlin_utils/marlin.cuh"
    try:
        with open(marlin_header, 'r') as f:
            content = f.read()
        
        # Check for device inline functions
        checks = [
            "__device__ inline void cp_async4(",
            "__device__ inline void cp_async_fence(",
            "__device__ inline void cp_async_wait(",
            "memcpy(smem_ptr, glob_ptr, 16)",
        ]
        
        all_found = True
        for check in checks:
            if check in content:
                print(f"✅ Found: {check.split('(')[0]}")
            else:
                print(f"❌ Missing: {check.split('(')[0]}")
                all_found = False
        
        return all_found
        
    except Exception as e:
        print(f"❌ Failed to check Marlin header: {e}")
        return False

def test_bf16_operators_fix():
    """Test BF16 operators fix."""
    print("\n🔧 Testing BF16 operators fix...")
    
    compatibility_header = "custom_ops/gpu_ops/moe/moe_compatibility.h"
    try:
        with open(compatibility_header, 'r') as f:
            content = f.read()
        
        # Check for BF16 and FP16 operators
        checks = [
            "__nv_bfloat16& operator*=(__nv_bfloat16& a, float b)",
            "__nv_bfloat16& operator+=(__nv_bfloat16& a, const __nv_bfloat16& b)",
            "__half& operator*=(__half& a, float b)",
            "__half& operator+=(__half& a, const __half& b)",
        ]
        
        all_found = True
        for check in checks:
            if check in content:
                print(f"✅ Found: {check.split('(')[0]}")
            else:
                print(f"❌ Missing: {check.split('(')[0]}")
                all_found = False
        
        return all_found
        
    except Exception as e:
        print(f"❌ Failed to check compatibility header: {e}")
        return False

def test_individual_file_compilation():
    """Test compilation of individual problematic files."""
    print("\n🔨 Testing individual file compilation...")
    
    cc = get_compute_capability()
    print(f"Using compute capability: {cc}")
    
    test_files = [
        "custom_ops/gpu_ops/moe/gptq_marlin_repack.cu",
        "custom_ops/gpu_ops/moe/moe_deepgemm_depermute.cu",
        "custom_ops/gpu_ops/moe/moe_simple_cc70.cu",
    ]
    
    all_passed = True
    
    for test_file in test_files:
        if not os.path.exists(test_file):
            print(f"⚠️  File not found: {test_file}")
            continue
        
        print(f"\n  Testing: {os.path.basename(test_file)}")
        
        try:
            nvcc_cmd = [
                "nvcc", "-c", test_file,
                "-o", f"test_{os.path.basename(test_file)}.o",
                "-std=c++17",
                f"-gencode=arch=compute_{cc},code=sm_{cc}",
                "-Icustom_ops/gpu_ops",
                "-Icustom_ops/gpu_ops/moe",
                "-DMOE_COMPATIBILITY_CC70=1" if cc < 80 else "",
            ]
            
            # Remove empty strings
            nvcc_cmd = [arg for arg in nvcc_cmd if arg]
            
            result = subprocess.run(nvcc_cmd, capture_output=True, text=True, cwd=".")
            
            # Clean up
            obj_file = f"test_{os.path.basename(test_file)}.o"
            if os.path.exists(obj_file):
                os.remove(obj_file)
            
            if result.returncode == 0:
                print(f"    ✅ Compilation successful")
            else:
                print(f"    ❌ Compilation failed:")
                print(f"    STDERR: {result.stderr[:200]}...")
                all_passed = False
                
        except Exception as e:
            print(f"    ❌ Test failed: {e}")
            all_passed = False
    
    return all_passed

def test_setup_ops_syntax():
    """Test setup_ops.py syntax and basic functionality."""
    print("\n📝 Testing setup_ops.py...")
    
    try:
        # Test Python syntax
        result = subprocess.run([
            sys.executable, "-m", "py_compile", "custom_ops/setup_ops.py"
        ], capture_output=True, text=True, cwd=".")
        
        if result.returncode == 0:
            print("✅ setup_ops.py syntax is valid")
        else:
            print("❌ setup_ops.py syntax error:")
            print(result.stderr)
            return False
        
        # Test help command
        os.chdir("custom_ops")
        result = subprocess.run([
            sys.executable, "setup_ops.py", "--help"
        ], capture_output=True, text=True, timeout=10)
        os.chdir("..")
        
        if result.returncode == 0:
            print("✅ setup_ops.py can be executed")
            return True
        else:
            print("❌ setup_ops.py execution failed")
            return False
            
    except subprocess.TimeoutExpired:
        print("✅ setup_ops.py --help timed out (normal)")
        return True
    except Exception as e:
        print(f"❌ setup_ops.py test failed: {e}")
        return False

def test_python_backend():
    """Test Python backend import."""
    print("\n🐍 Testing Python backend...")
    
    try:
        sys.path.insert(0, '.')
        from fastdeploy.model_executor.layers.moe.fused_moe_cc70_backend import CC70CompatMoEMethod
        from fastdeploy.model_executor.layers.moe.moe import get_moe_method, _get_cuda_compute_capability
        
        print("✅ Successfully imported MOE backends")
        
        # Test compute capability detection
        cc = _get_cuda_compute_capability()
        print(f"✅ Detected compute capability: {cc}")
        
        # Test method selection
        moe_method = get_moe_method()
        method_name = type(moe_method).__name__
        print(f"✅ Selected MOE method: {method_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Python backend test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("Comprehensive MOE cc70 Compatibility Test")
    print("=" * 45)
    
    if not paddle.is_compiled_with_cuda():
        print("❌ CUDA not available, skipping tests")
        return False
    
    cc = get_compute_capability()
    print(f"🖥️  Detected GPU compute capability: {cc}")
    
    if cc >= 80:
        print("ℹ️  Your GPU supports full MOE features")
    elif cc >= 70:
        print("ℹ️  Your GPU will use compatibility mode")
    else:
        print("⚠️  Your GPU has limited MOE support")
    
    tests = [
        ("Required Files", test_required_files),
        ("Marlin Async Fix", test_marlin_async_fix),
        ("BF16 Operators Fix", test_bf16_operators_fix),
        ("Individual File Compilation", test_individual_file_compilation),
        ("setup_ops.py Syntax", test_setup_ops_syntax),
        ("Python Backend", test_python_backend),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Running {test_name} test...")
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 45)
    print("Test Summary:")
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! MOE cc70 compatibility is ready.")
        print("\n💡 Next steps:")
        print("   1. cd custom_ops")
        print("   2. python setup_ops.py build_ext --inplace")
        print("   3. Test with your MOE models")
        return True
    else:
        print("\n⚠️  Some tests failed. Please check the fixes above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
